import path from 'path'
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import svgLoader from 'vite-svg-loader'
import { inlineCSSPlugin } from './vite-plugins/inline-css-plugin'

export default defineConfig({
  plugins: [
    vue(),
    svgLoader({
      defaultImport: 'url',
    }),
    inlineCSSPlugin(), // 内联CSS插件
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
    },
  },
  build: {
    // 输出目录
    outDir: 'dist-widget',
    // 清理输出目录
    emptyOutDir: true,
    // 构建为library模式
    lib: {
      entry: 'widget/index.ts', // 使用新的Widget入口文件
      name: 'EaseAIWidget',
      fileName: 'easeai-widget',
      formats: ['iife'], // 立即执行函数，适合script标签加载
    },
    rollupOptions: {
      // 排除不必要的依赖
      external: [],
      output: {
        // 全局变量名称
        globals: {},
        // 自定义输出文件名
        entryFileNames: 'easeai-widget.js',
        // 内联所有依赖，创建单个文件
        inlineDynamicImports: true,
        // 确保代码可以在老版本浏览器中运行
        format: 'iife',
        // 手动分割代码，排除大型依赖
        manualChunks: undefined,
      },
    },
    // 目标浏览器
    target: 'es2015',
    // 启用CSS代码分割，但对于widget，我们可能想要内联CSS
    cssCodeSplit: false,
    // 生成source map用于调试
    sourcemap: false,
    // 最小化输出
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true, // 移除console.log
        drop_debugger: true, // 移除debugger
        pure_funcs: ['console.log', 'console.info', 'console.debug'], // 移除特定函数调用
        reduce_vars: true,
        reduce_funcs: true,
        collapse_vars: true,
        // 移除未使用的代码
        dead_code: true,
        // 移除未使用的函数参数
        unused: true,
      },
      mangle: {
        // 压缩变量名
        toplevel: true,
      },
    },
    // Widget不需要复制public目录
    copyPublicDir: false,
    // 设置更严格的代码分割阈值
    chunkSizeWarningLimit: 500, // 500KB警告阈值
  },
  define: {
    // 定义环境变量
    'process.env.NODE_ENV': '"production"',
    __VUE_OPTIONS_API__: false, // 禁用选项式API减小体积
    __VUE_PROD_DEVTOOLS__: false,
    __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: false,
  },
  // 优化依赖预构建
  optimizeDeps: {
    include: [], // 只包含必需的依赖
    exclude: [
      // 排除大型依赖
      'naive-ui',
      'element-plus',
      '@element-plus/icons-vue',
      'markdown-it',
      'katex',
      'highlight.js',
      'mermaid',
      'vue-router',
      'pinia',
      '@vueuse/core',
    ],
  },
})
