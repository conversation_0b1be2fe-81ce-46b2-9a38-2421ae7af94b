<script setup lang="ts">
import { log } from 'console'
import { useRoute, useRouter } from 'vue-router'
import type { Ref } from 'vue'
import { h, inject, markRaw, onMounted, onUnmounted, ref, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import HeaderActions from './components/HeaderActions.vue'
import ModelSettings from './components/ModelSettings.vue'
import PreviewDebug from './components/PreviewDebug.vue'
import PromptEditor from './components/PromptEditor.vue'
import AgentThemeDialog from './components/AgentThemeDialog.vue'
import AuditDialog from './components/AuditDialog.vue'
import KnowledgeBase from './components/knowledgeBase.vue'
import {
  getBizAgentCategoryList,
  getBizAgentDetailId,
  postBizAgentAddOrUpdateAgent,
} from '@/api/agent'
import { useSettingStore } from '@/store'
import '@/typings/knowledge-base.d.ts'

const props = defineProps<{
  isReview: boolean
}>()
const router = useRouter()
const route = useRoute()

// 定义防抖timer
const timer = ref<NodeJS.Timeout | null>(null)

// 定义表单是否已修改
const formChanged = ref(false)

// 获取父组件提供的headerActions引用，添加类型注解
const headerActions = inject<Ref<any>>('headerActions')

// 获取路由参数中的id
const agentId = ref<string | undefined>(undefined)

// 定义分类选择变量
// resolved category改为agentInfo下的属性，而不是单独列出
// 定义分类列表
const categoryList = ref<Agent.AgentCategoryVo[]>([])

// 定义模型设置相关变量
const modelConfig = ref({
  modelId: '',
  temperature: 1.0,
  maxTokens: 4096,
  singleReplyLimitFlag: true,
  maxContextCount: 3,
})

// 定义知识库列表
const kbList = ref<KnowledgeBase.KnowledgeBaseVo[]>([])

// 定义知识库设置
const kbSettings = ref({
  maxResults: 3,
  minScore: 0.5,
  rerank: false,
  rerankModelId: undefined as number | undefined,
})

// 定义对话框显示状态
const themeDialogVisible = ref(false)
const auditDialogVisible = ref(false)
// 根据Agent.AgentVo定义agentInfo
const agentInfo = ref<Agent.AgentVo>({
  name: '',
  description: '',
  emoji: '',
  backgroundColor: '#FFEBD4',
  categoryIdList: [],
  isPrivate: false,
  prompt: '',
  modelId: '',
  modelConfig: '',
  status: undefined,
})

// 定义本地存储的key - 单一key，不再与agentId相关
const STORAGE_KEY = 'agent_form_draft'

// 保存表单数据到本地存储
const saveFormToStorage = () => {
  if (!localStorage.getItem('use_agent_storage')) return
  const formData = {
    agentInfo: agentInfo.value,
    modelConfig: modelConfig.value,
    kbList: kbList.value, // 包含知识库列表
    agentId: agentId.value, // 存储当前编辑的agentId
    timestamp: new Date().getTime(),
  }
  sessionStorage.setItem(STORAGE_KEY, JSON.stringify(formData))
  formChanged.value = true // 标记表单已修改
}

// 从本地存储恢复表单数据
// （现在默认关闭这个逻辑）
const restoreFormFromStorage = () => {
  if (!localStorage.getItem('use_agent_storage')) return

  const storedData = sessionStorage.getItem(STORAGE_KEY)
  if (!storedData) return

  try {
    const {
      agentInfo: storedAgentInfo,
      modelConfig: storedModelConfig,
      kbList: storedKbList,
      agentId: storedAgentId,
    } = JSON.parse(storedData)

    // 只有当存储的 agentId 与当前一致或者都是未定义时才恢复数据
    if ((agentId.value && storedAgentId === agentId.value) || (!agentId.value && !storedAgentId)) {
      // 恢复数据
      agentInfo.value = { ...agentInfo.value, ...storedAgentInfo }
      modelConfig.value = { ...modelConfig.value, ...storedModelConfig }
      if (storedKbList && Array.isArray(storedKbList)) {
        kbList.value = storedKbList
      }
    }
  } catch (error) {
    console.error('恢复表单数据失败:', error)
  }
}

// 清除本地存储的表单数据
const clearFormStorage = () => {
  sessionStorage.removeItem(STORAGE_KEY)
  formChanged.value = false
}

// 监听表单数据变化，自动保存，使用防抖优化性能
const autoSave = () => {
  if (timer.value) clearTimeout(timer.value)

  timer.value = setTimeout(() => {
    saveFormToStorage()
  }, 1000)
}

// 监听表单数据变化，自动保存
watch(
  [agentInfo, modelConfig, kbList],
  (newVal, oldVal) => {
    // resolved 刚进页面时候会触发autoSave，不合理，应该我修改过才触发
    // console.log('autoSave', newVal, oldVal)
    // console.log('autoSave', JSON.parse(JSON.stringify(newVal)), JSON.parse(JSON.stringify(oldVal)))
    autoSave()
  },
  { deep: true },
)

// 获取分类列表
const getCategoryList = async () => {
  try {
    const res = await getBizAgentCategoryList()
    if (res.code === 200 && res.data) {
      categoryList.value = res.data as Agent.AgentCategoryVo[]
    } else {
      console.error('获取分类列表失败:', res.msg)
    }
  } catch (error) {
    console.error('获取分类列表失败:', error)
  }
}

// 根据分类ID获取分类名称
const getCategoryName = (categoryId: string) => {
  const category = categoryList.value.find(item => item.id === categoryId)
  return category?.name || '未知分类'
}

// 获取助手详情
const getAgentDetail = async (id: string) => {
  try {
    const res = await getBizAgentDetailId<Agent.AgentVo>({ id })
    if (res.code === 200 && res.data) {
      const agentData = res.data
      // 更新表单数据
      agentInfo.value = {
        name: agentData.name,
        description: agentData.description,
        emoji: agentData.emoji,
        backgroundColor: agentData.backgroundColor,
        categoryIdList: agentData.categoryIdList,
        isPrivate: agentData.status === -1, // 根据status判断是否为私有助手
        status: agentData.status,
        prompt: agentData.prompt,
      }
      console.log('agentInfo.status', agentInfo.value.status)
      // 更新模型配置
      // 从agentData中提取模型配置字段
      modelConfig.value = {
        modelId: agentData.modelId && agentData.modelValid ? String(agentData.modelId) : '',
        temperature: agentData.temperature || 1.0,
        maxContextCount: agentData.maxContextCount,
        singleReplyLimitFlag: !!agentData.maxTokens,
        maxTokens: agentData.maxTokens || 4096,
      }

      // 更新知识库列表
      if (agentData.kbList && Array.isArray(agentData.kbList)) {
        kbList.value = agentData.kbList
      }

      // 更新知识库设置
      if (agentData.maxResults !== undefined) kbSettings.value.maxResults = agentData.maxResults
      if (agentData.minScore !== undefined) kbSettings.value.minScore = agentData.minScore
      if (agentData.rerankModelId) {
        // 后端不返回rerank
        kbSettings.value.rerank = true
        kbSettings.value.rerankModelId = agentData.rerankModelId
      } else {
        kbSettings.value.rerank = false
        kbSettings.value.rerankModelId = undefined
      }
      // if (agentData.modelConfig) {
      //   try {
      //     const config = JSON.parse(agentData.modelConfig)
      //     modelConfig.value = {
      //       ...modelConfig.value,
      //       ...config,
      //     }
      //   } catch (e) {
      //     console.error('解析模型配置失败:', e)
      //   }
      // }
    }
  } catch (error) {
    console.error('获取助手详情失败:', error)
  }
}

// 返回按钮点击事件
const handleBack = () => {
  router.back()
}

// 历史记录按钮点击事件
const handleHistory = () => {
  console.log('历史记录按钮被点击')
  // 这里可以添加历史记录逻辑
}

// 下拉菜单点击事件
const handleDropdownSelect = (key: string) => {
  console.log('下拉菜单选项被点击:', key)
  // 这里可以添加下拉菜单逻辑
}

// 编辑按钮点击事件
const handleEditAgent = () => {
  // 打开对话框时传入当前的 agentInfo 数据
  themeDialogVisible.value = true
}

// 保存主题设置
const handleThemeSave = (data: Agent.AgentVo) => {
  // 更新本地数据
  agentInfo.value = {
    ...agentInfo.value,
    ...data,
  }
  // handleSave({ shouldRedirect: false, showMessage: false })
}

// 创建header-actions内容 - 使用组件
const createHeaderActions = () => {
  return h(HeaderActions, {
    onBack: handleBack,
    onHistory: handleHistory,
    onDropdown: handleDropdownSelect,
    isReview: props.isReview,
    agentId: agentId.value,
    isPrivate: agentInfo.value.isPrivate,
    status: agentInfo.value.status,
    onAudit: () => {
      auditDialogVisible.value = true
    },
    onSaveWithPrivate: (isPrivate: boolean) => {
      // 更新表单中的isPrivate值
      agentInfo.value.isPrivate = isPrivate
      handleSave()
    },
  })
}

// 保存按钮点击事件
const handleSave = async ({
  shouldRedirect = true,
  showMessage = true,
}: { shouldRedirect?: boolean; showMessage?: boolean } = {}) => {
  // 表单验证
  if (!agentInfo.value.name?.trim()) {
    themeDialogVisible.value = true
    if (showMessage) {
      ElMessage.warning('请输入助手名称')
    }
    return
  }
  if (!agentInfo.value.description?.trim()) {
    themeDialogVisible.value = true
    if (showMessage) {
      ElMessage.warning('请输入助手功能介绍')
    }
    return
  }
  if (!agentInfo.value.emoji) {
    themeDialogVisible.value = true
    if (showMessage) {
      ElMessage.warning('请选择图标')
    }
    return
  }
  if (!agentInfo.value.prompt?.trim()) {
    if (showMessage) {
      ElMessage.warning('请输入提示词内容')
    }
    return
  }
  // 验证分类是否选择
  if (!agentInfo.value.categoryIdList?.length) {
    if (showMessage) {
      ElMessage.warning('请选择助手分类')
    }
    return
  }

  try {
    // 构建请求参数
    // resolved 加上modelConfig里的字段和kbIdList
    const params: Agent.AgentVo = {
      name: agentInfo.value.name,
      description: agentInfo.value.description,
      emoji: agentInfo.value.emoji,
      backgroundColor: agentInfo.value.backgroundColor,
      prompt: agentInfo.value.prompt,
      isPrivate: agentInfo.value.isPrivate,
      categoryIdList: agentInfo.value.categoryIdList,
      // 添加modelConfig里的字段
      modelId: modelConfig.value.modelId,
      temperature: modelConfig.value.temperature,
      maxTokens: modelConfig.value.singleReplyLimitFlag ? modelConfig.value.maxTokens : 0, // 后端不接受undefined
      maxContextCount: modelConfig.value.maxContextCount,
      // 知识库-添加知识库ID列表
      kbIdList: kbList.value.map(kb => kb.id).filter(Boolean) as string[],
      // 知识库-添加知识库设置
      maxResults: kbSettings.value.maxResults,
      minScore: kbSettings.value.minScore,
      rerankModelId: kbSettings.value.rerank ? kbSettings.value.rerankModelId : undefined, // 后端接受undefined
    }

    // 如果是编辑模式，添加id
    if (agentId.value) {
      params.id = agentId.value
    }

    // 调用接口
    const res = await postBizAgentAddOrUpdateAgent<API.Response<any>>(params)

    if (res.code === 200) {
      clearFormStorage() // 保存成功后清除本地存储
      ElMessage.success('保存成功')
      formChanged.value = false
      if (shouldRedirect) {
        router.back()
      }
    }
  } catch (error) {
    console.error('保存助手失败:', error)
  }
}

// 审核成功回调
const handleAuditSuccess = () => {
  router.back()
}

// 添加路由离开前确认机制
// resolved 点击返回会触发两次，修复方法是只在onMounted中注册一次
const confirmLeaving = (to: any, from: any, next: any) => {
  // 获取当前页面路径 - 正确的路径是/agent/agent-operation
  const currentPath = '/agent/agent-operation'

  console.log('路由守卫触发', {
    formChanged: formChanged.value,
    fromPath: from.path,
    toPath: to.path,
    currentPath,
  })

  if (formChanged.value && from.path === currentPath && to.path !== currentPath) {
    // todo 依旧没看到弹框
    ElMessageBox.confirm('您有未保存的更改，确认离开吗？', '提示', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(() => {
        clearFormStorage()
        next()
      })
      .catch(() => {
        next(false)
      })
  } else {
    next()
  }
}

// 存储路由守卫移除函数
let removeRouterGuard: Function | null = null

// 不在这里注册路由守卫，而仅在onMounted中注册一次
// router.beforeEach(confirmLeaving)

// resolved 如果是新增助手的话，themeDialogVisible.value = true
onMounted(() => {
  // 解释一下，为什么这里需要使用markRaw
  // 因为headerActions是一个ref，如果直接赋值给headerActions.value，会导致组件的更新无法触发
  // 所以需要使用markRaw，将headerActions转换为原始值，这样组件的更新才能触发
  if (headerActions) {
    headerActions.value = markRaw(createHeaderActions())
  }
  // 获取分类列表
  getCategoryList()

  // 检查是否有id参数，如果有则获取助手详情
  if (route.query.id) {
    const id = route.query.id
    if (id) {
      agentId.value = id.toString()
      getAgentDetail(id.toString()).then(() => {
        restoreFormFromStorage()
      })
    }
  } else {
    restoreFormFromStorage()
    // 如果是新增助手，自动打开主题设置对话框
    themeDialogVisible.value = true

    // 获取默认模型ID并设置
    // if (!modelConfig.value.modelId) {
    //   const settingStore = useSettingStore()
    //   settingStore.fetchDefaultModel(false).then(modelData => {
    //     if (modelData?.chatModelId) {
    //       // 将number类型转为string
    //       modelConfig.value.modelId = modelData.chatModelId
    //     }
    //   })
    // }
  }

  // 正确注册路由守卫，并保存移除函数
  if (localStorage.getItem('use_agent_storage')) {
    removeRouterGuard = router.beforeEach(confirmLeaving)
  }
})

// 监听agentId变化，当agentId变化时更新HeaderActions组件
watch(agentId, newId => {
  console.log('agentId变化', newId)
  if (headerActions && newId) {
    headerActions.value = markRaw(createHeaderActions())
  }
})

// 监听agentInfo.isPrivate变化，当isPrivate变化时更新HeaderActions组件
watch(
  () => agentInfo.value.isPrivate,
  () => {
    if (headerActions) {
      headerActions.value = markRaw(createHeaderActions())
    }
  },
)

// 监听agentInfo.status变化，当status变化时更新HeaderActions组件
watch(
  () => agentInfo.value.status,
  newStatus => {
    console.log('status变化:', newStatus)
    if (headerActions) {
      headerActions.value = markRaw(createHeaderActions())
    }
  },
)

onUnmounted(() => {
  if (headerActions) {
    headerActions.value = null
  }

  // 正确移除路由守卫
  if (removeRouterGuard) {
    removeRouterGuard()
  }
})
</script>

<template>
  <div class="my-container">
    <!-- top-container按需求移除了 -->
    <div class="bottom-container">
      <div class="tab-column">
        <div class="agent-box">
          <div class="agent-info">
            <div
              class="emoji-wrap !w-[72px] !h-[72px]"
              :style="{ backgroundColor: agentInfo.backgroundColor }"
            >
              <img v-if="agentInfo.emoji" :src="agentInfo.emoji" class="emoji" />
            </div>
            <div class="info">
              <div class="name">
                <span>{{ agentInfo.name || '未命名助手' }}</span>
                <div v-if="!isReview" class="edit-btn" @click="handleEditAgent">
                  <img src="@/assets/agent/edit.svg" alt="edit" />
                </div>
              </div>
              <div class="category-tags">
                <el-tag
                  v-for="categoryId in agentInfo.categoryIdList"
                  :key="categoryId"
                  class="category-tag"
                  type="info"
                  effect="plain"
                >
                  {{ getCategoryName(categoryId) }}
                </el-tag>
                <span v-if="!agentInfo.categoryIdList?.length" class="no-category">未选择分类</span>
              </div>
            </div>
          </div>
          <div class="instruction">{{ agentInfo.description || '请编辑助手介绍' }}</div>
        </div>
        <PromptEditor
          v-model="agentInfo.prompt"
          :disabled="isReview"
          :placeholder="isReview ? '审核模式下不可编辑提示词' : '请输入提示词'"
        />
      </div>
      <div class="tab-column">
        <div class="tab-header">
          <div class="title">模型设置</div>
        </div>
        <div class="tab-content">
          <ModelSettings v-model:model-config="modelConfig" :disabled="isReview" />
        </div>
        <KnowledgeBase
          v-model="kbList"
          v-model:knowledge-base-settings="kbSettings"
          :disabled="isReview"
        />
      </div>
      <div class="tab-column">
        <div class="tab-header">
          <div class="title">预览与调试</div>
        </div>
        <div class="tab-content">
          <PreviewDebug
            :model-config="modelConfig"
            :prompt-content="agentInfo.prompt"
            :disabled="isReview"
          />
        </div>
      </div>
    </div>
    <AgentThemeDialog
      v-model:visible="themeDialogVisible"
      :initial-data="{
        name: agentInfo.name || '',
        description: agentInfo.description || '',
        emoji: agentInfo.emoji || '',
        backgroundColor: agentInfo.backgroundColor || '#FFEBD4',
        categoryIdList: agentInfo.categoryIdList || [],
      }"
      @save="handleThemeSave"
    />
    <AuditDialog
      v-model="auditDialogVisible"
      :agent-id="agentId"
      :category-id-list="agentInfo.categoryIdList"
      :model-id="modelConfig.modelId"
      @success="handleAuditSuccess"
    />
  </div>
</template>

<style lang="less" scoped>
.my-container {
  padding: 20px;
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
}
.bottom-container {
  display: flex;
  align-items: stretch;
  justify-content: space-between;
  width: 100%;
  background: #ffffff;
  border-radius: 8px;
  flex: 1;
  overflow: hidden;
  min-height: 0;
}
.agent-box {
  padding: 16px;
  border-bottom: 1px solid #dadde8;
  .agent-info {
    display: flex;
    align-items: center;
    .info {
      margin-left: 16px;
      .name {
        display: flex;
        align-items: center;
        font-size: 18px;
        color: #30343a;
        line-height: 25px;
        font-weight: 500;
        .edit-btn {
          cursor: pointer;
          margin-left: 5px;
        }
      }
    }
  }
  .instruction {
    margin-top: 8px;
    font-size: 14px;
    color: #999999;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
  }
  .category-tags {
    margin-top: 8px;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;

    .category-tag {
      font-size: 12px;
      color: #666;
      background-color: #f5f5f5;
      border: 1px solid #d9d9d9;
    }

    .no-category {
      font-size: 14px;
      color: #999;
    }
  }
}
</style>

<style lang="less">
.tab-column {
  width: 33.33%;
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 0;
  .tab-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 16px 0;
    flex-shrink: 0;
    box-sizing: border-box;
    height: 40px;

    .title {
      font-size: 18px;
      color: #30343a;
      line-height: 25px;
      position: relative;
      margin-left: -16px;
      padding-left: 16px;
      font-weight: 500;
      &:before {
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        content: '';
        display: block;
        width: 4px;
        height: 24px;
        background: #4865e8;
      }
    }
  }

  .tab-content {
    flex: 1;
    overflow-y: auto;
    margin-top: 20px;
    padding: 0 16px 16px;
    min-height: 0;

    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 5px;
    }

    &::-webkit-scrollbar-thumb {
      background: #e0e0e0;
      border-radius: 2px;
      cursor: pointer;

      &:hover {
        background: #c0c0c0;
      }
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    // 当不滚动时隐藏滚动条
    &:hover {
      &::-webkit-scrollbar-thumb {
        background: #e0e0e0;
      }
    }
  }
}
.tab-column + .tab-column {
  border-left: 1px solid #dadde8;
}
</style>
