/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AboutTab: typeof import('./src/components/common/SettingsDialog/AboutTab.vue')['default']
    AppSider: typeof import('./src/components/common/AppSider/index.vue')['default']
    Button: typeof import('./src/components/common/HoverButton/Button.vue')['default']
    ConversationStatusIndicator: typeof import('./src/components/common/ConversationStatusIndicator/index.vue')['default']
    ElAlert: typeof import('element-plus/es')['ElAlert']
    ElBadge: typeof import('element-plus/es')['ElBadge']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElColorPicker: typeof import('element-plus/es')['ElColorPicker']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElLink: typeof import('element-plus/es')['ElLink']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioButton: typeof import('element-plus/es')['ElRadioButton']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSkeleton: typeof import('element-plus/es')['ElSkeleton']
    ElSkeletonItem: typeof import('element-plus/es')['ElSkeletonItem']
    ElSlider: typeof import('element-plus/es')['ElSlider']
    ElSpace: typeof import('element-plus/es')['ElSpace']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElTree: typeof import('element-plus/es')['ElTree']
    ElTreeSelect: typeof import('element-plus/es')['ElTreeSelect']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    EmojiPickerLocal: typeof import('./src/components/EmojiPickerLocal.vue')['default']
    EnableSwitch: typeof import('./src/components/common/EnableSwitch.vue')['default']
    FileTypePic: typeof import('./src/components/common/FileTypePic.vue')['default']
    FileUpload: typeof import('./src/components/common/FileUpload/index.vue')['default']
    HoverButton: typeof import('./src/components/common/HoverButton/index.vue')['default']
    ImageUpload: typeof import('./src/components/common/ImageUpload/index.vue')['default']
    LoginDialog: typeof import('./src/components/common/LoginDialog/index.vue')['default']
    MarkdownEditor: typeof import('./src/components/MarkdownEditor.vue')['default']
    NaiveProvider: typeof import('./src/components/common/NaiveProvider/index.vue')['default']
    NInput: typeof import('naive-ui')['NInput']
    NMenu: typeof import('naive-ui')['NMenu']
    NStep: typeof import('naive-ui')['NStep']
    NSteps: typeof import('naive-ui')['NSteps']
    Pagination: typeof import('./src/components/common/Pagination/index.vue')['default']
    ProfileTab: typeof import('./src/components/common/SettingsDialog/ProfileTab.vue')['default']
    PromptStore: typeof import('./src/components/common/PromptStore/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SecurityTab: typeof import('./src/components/common/SettingsDialog/SecurityTab.vue')['default']
    Selector: typeof import('./src/components/Selector.vue')['default']
    SettingsDialog: typeof import('./src/components/common/SettingsDialog/index.vue')['default']
    SlideCaptcha: typeof import('./src/components/common/SlideCaptcha/index.vue')['default']
    StatisticsTab: typeof import('./src/components/common/SettingsDialog/StatisticsTab.vue')['default']
    SvgIcon: typeof import('./src/components/common/SvgIcon/index.vue')['default']
    UserAvatar: typeof import('./src/components/common/UserAvatar/index.vue')['default']
    UserInfoDialog: typeof import('./src/components/common/UserInfoDialog.vue')['default']
  }
  export interface ComponentCustomProperties {
    vInfiniteScroll: typeof import('element-plus/es')['ElInfiniteScroll']
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
