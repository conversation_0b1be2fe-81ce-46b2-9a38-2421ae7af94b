# EaseAI Widget

独立的客服聊天Widget组件，支持嵌入到任何网站。

## 📁 目录结构

```
widget/
├── Widget.vue           # 主Widget组件（包含完整样式）
├── index.ts            # Widget入口文件
├── style-loader.ts     # 样式加载器
├── test.vue           # 测试组件
├── utils/             # 工具函数
│   └── streamHandler.ts # SSE流式响应处理器
└── README.md          # 本文档
```

## 🚀 构建

```bash
# 构建Widget
pnpm run build:widget

# 输出文件
dist-widget/easeai-widget.js  # Widget主文件
dist-widget/style.css         # Widget样式
```

## 📋 使用方式

### 1. 基本使用

```html
<script
  src="./dist-widget/easeai-widget.js"
  data-agent-id="your-agent-id"
  data-client-id="your-client-id"
  data-api-endpoint="https://your-api.com/prod-api"
  data-static-url="https://your-static-url.com">
</script>
```

### 2. 编程方式

```javascript
// 初始化Widget
window.EaseAI.init({
  agentId: 'your-agent-id',
  clientId: 'your-client-id',
  title: '在线客服',
  subtitle: '我们将为您提供帮助',
  agentAvatar: 'https://your-domain.com/agent-avatar.png', // AI助手头像
  userAvatar: 'https://your-domain.com/user-avatar.png',   // 用户头像
  agentBackgroundColor: '#4c5cec', // AI助手头像背景色
  welcomeMessage: '您好！有什么可以帮助您的吗？',
  inputPlaceholder: '请输入您的问题...',
  defaultMessage: '产品咨询,技术支持,售后服务', // 快捷消息，逗号分割
  apiEndpoint: 'https://your-api.com/prod-api',
  staticUrl: 'https://your-static-url.com'
})

// 控制Widget
window.EaseAI.show()
window.EaseAI.hide()
window.EaseAI.toggle()
```

## ⚙️ 配置选项

| 参数                   | 类型   | 必填 | 默认值               | 说明                   |
| ---------------------- | ------ | ---- | -------------------- | ---------------------- |
| `agentId`              | string | ✅    | -                    | AI助手ID               |
| `clientId`             | string | ❌    | 默认值               | 客户端ID               |
| `title`                | string | ❌    | '在线客服'           | Widget标题             |
| `subtitle`             | string | ❌    | '我们将为您提供帮助' | Widget副标题           |
| `avatar`               | string | ❌    | -                    | 通用头像（兼容旧版本） |
| `agentAvatar`          | string | ❌    | -                    | AI助手头像URL          |
| `userAvatar`           | string | ❌    | -                    | 用户头像URL            |
| `agentBackgroundColor` | string | ❌    | '#4c5cec'            | AI助手头像背景色       |
| `welcomeMessage`       | string | ❌    | -                    | 欢迎消息               |
| `inputPlaceholder`     | string | ❌    | '请输入您的问题...'  | 输入框占位符           |
| `defaultMessage`       | string | ❌    | -                    | 快捷消息，逗号分割     |
| `position`             | string | ❌    | 'bottom-right'       | Widget位置             |
| `theme`                | string | ❌    | 'light'              | 主题：light/dark       |
| `primaryColor`         | string | ❌    | '#4c5cec'            | 主题色                 |
| `apiEndpoint`          | string | ❌    | '/prod-api'          | API端点                |
| `password`             | string | ❌    | -                    | 访问密码（对外发布时） |
| `staticUrl`            | string | ❌    | -                    | 静态资源URL前缀        |

### 头像优先级说明

**AI助手头像优先级**：
1. `agentAvatar` - 配置的AI助手专用头像
2. `avatar` - 通用头像配置（向后兼容）
3. 服务器返回的 `agentEmoji`
4. 默认生成的头像

**用户头像优先级**：
1. `userAvatar` - 配置的用户头像
2. 默认生成的头像

**背景色优先级**：
1. `agentBackgroundColor` - 配置的背景色
2. 服务器返回的 `agentBackgroundColor`
3. 默认主题色 `#4c5cec`

## 🔧 核心功能

- ✅ **SSE流式响应**：实时显示AI回复
- ✅ **Shadow DOM隔离**：样式完全隔离，不影响宿主页面
- ✅ **响应式设计**：支持移动端和桌面端
- ✅ **头像定制**：支持自定义AI助手和用户头像及背景色
- ✅ **主题定制**：支持浅色/深色主题
- ✅ **多语言支持**：支持中英文界面
- ✅ **错误处理**：完善的错误处理和重试机制

## 🌟 技术特性

- **Vue 3 + TypeScript**：现代化开发体验
- **Vite构建**：快速构建和热更新
- **单文件输出**：所有依赖打包为单个JS文件
- **体积优化**：gzip后仅27KB
- **浏览器兼容**：支持ES2015+的现代浏览器
